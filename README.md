# Tiltrotor Drone Gazebo Fortress Simulation

This project contains a native Gazebo Fortress simulation for a tiltrotor drone designed for contact technology inspection missions. The drone features tilting rotors that can transition between vertical (hover) and horizontal (forward flight) positions. This version uses native Gazebo Fortress installation with updated tiltrotor configuration parameters.

## Project Structure

```
├── drone_model/              # Your tiltrotor drone model
│   ├── meshes/
│   │   ├── body.dae         # Main body mesh from FreeCAD
│   │   ├── propeller.dae    # Propeller mesh
│   │   └── tilt_rotor.dae   # Tilt rotor mechanism mesh
│   ├── model.config         # Model configuration
│   └── model.sdf           # Gazebo SDF model definition
├── x3_uav/                  # Reference quadrotor model
├── worlds/                  # Gazebo world files
│   └── tiltrotor_world.sdf # Main simulation world
├── launch/                  # Launch scripts
│   └── launch_tiltrotor_sim.sh
├── install_dependencies.sh  # Dependency installation script
├── run_simulation.sh        # Main simulation runner
└── test_tiltrotor.sh        # Test script
```

## Drone Configuration

### Physical Properties
- **Total Mass**: 4.5 kg
- **Inertia Matrix**:
  - Ixx: 0.0144 kg⋅m²
  - Iyy: 0.0434 kg⋅m²
  - Izz: 0.0410 kg⋅m²

### Tiltrotor Positions
- **Front Left (UL)**: (0.245, -0.345, 0.0075) m
- **Front Right (UR)**: (0.245, 0.345, 0.0075) m
- **Rear Left (DL)**: (-0.260, -0.345, 0.0075) m
- **Rear Right (DR)**: (-0.260, 0.345, 0.0075) m

### Motor Configuration
- **Motor Offset**: (0.25, 0.345) m
- **Propeller Diameter**: 0.3556 m
- **Propeller Pitch**: 0.1219 m
- **Max Force**: 19.62 N per rotor
- **Rated Speed**: 5000 RPM

## Prerequisites

1. **Gazebo Fortress**: Ignition Gazebo installation (uses `ign gazebo` command)
2. **ROS2 Humble** (optional but recommended): For advanced features
3. **xmllint**: For SDF validation (usually included with libxml2)

## Quick Start

### 1. Install Dependencies

Run the automated installation script:

```bash
# Make the script executable
chmod +x install_dependencies.sh

# Run the installation script
./install_dependencies.sh
```

Or install manually:

**Ubuntu/Linux:**
```bash
# Install Gazebo Fortress
sudo apt-get update
sudo curl https://packages.osrfoundation.org/gazebo.gpg --output /usr/share/keyrings/pkgs-osrf-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/pkgs-osrf-archive-keyring.gpg] http://packages.osrfoundation.org/gazebo/ubuntu-stable $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/gazebo-stable.list > /dev/null
sudo apt-get update
sudo apt-get install -y ignition-fortress libxml2-utils
```

**macOS:**
```bash
# Install Gazebo Fortress via Homebrew
brew install ignition-fortress libxml2
```

### 2. Test the Setup

```bash
# Make scripts executable
chmod +x test_tiltrotor.sh run_simulation.sh

# Run the test script
./test_tiltrotor.sh
```

### 3. Start the Simulation

```bash
# Start with GUI (default)
./run_simulation.sh

# Or start headless (no GUI)
./run_simulation.sh --headless
```

## Model Features

### Tiltrotor Drone
- **Main body**: Uses your custom `body.dae` mesh from FreeCAD
- **Tilt rotors**: Two tilt rotor assemblies with `tilt_rotor.dae` meshes
- **Propellers**: Spinning propellers using `propeller.dae` meshes
- **Sensors**: IMU, air pressure sensor, magnetometer
- **Joints**:
  - Tilt joints: Allow rotors to tilt ±90 degrees
  - Propeller joints: Allow propellers to spin for thrust
- **Motor plugins**: Control propeller speed via ROS2 topics

### Control Commands

```bash
# Spin propellers
ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p 'velocity:[500, 500, 500, 500]'

# Control tilt angle (examples)
ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'
ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'
ign topic -t /model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'
ign topic -t /model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'

# List all available topics
ign topic -l

# Monitor sensor data
ign topic -e -t /imu
```

## Development

### Modifying the Model

1. Edit `drone_model/model.sdf` to adjust:
   - Joint positions and limits
   - Inertial properties
   - Sensor configurations
   - Motor parameters

2. Update mesh files in `drone_model/meshes/` with new exports from FreeCAD

3. Restart the simulation to see changes

### Adding New Features

- **Additional sensors**: Add to the base_link in `model.sdf`
- **More rotors**: Duplicate the tilt rotor assembly pattern
- **Control systems**: Add ROS2 nodes for autonomous control
- **PX4 integration**: Connect to PX4 SITL for flight controller testing

## Troubleshooting

### Installation Issues
- **Gazebo not found**: Make sure Gazebo is installed and in your PATH
- **Permission denied**: Make scripts executable with `chmod +x *.sh`
- **Missing dependencies**: Run `./install_dependencies.sh` to install required packages

### Model Loading Issues
- Verify mesh files are in correct locations: `drone_model/meshes/`
- Check SDF syntax: `xmllint --noout drone_model/model.sdf`
- Ensure all required files exist: `./test_tiltrotor.sh`

### Performance Issues
- Use headless mode for better performance: `./run_simulation.sh --headless`
- Adjust physics parameters in world file
- Reduce mesh complexity if needed

### Common Errors
- **"gz: command not found"**: Install Gazebo or check PATH
- **"Model not found"**: Check that you're running from the project root directory
- **"World file not found"**: Verify `worlds/tiltrotor_world.sdf` exists

## Next Steps

1. **Test the basic simulation** to ensure models load correctly
2. **Implement tilt control logic** for transition between hover and forward flight
3. **Add contact sensors** for inspection mission simulation
4. **Integrate with PX4** for realistic flight controller behavior
5. **Develop autonomous inspection algorithms**

## Contact Technology Integration

The model is designed for contact technology inspection where:
- Sensors maintain contact with surfaces during inspection
- Rotors can tilt to maintain stable contact while providing thrust
- Pressure and position feedback ensure proper contact force

This simulation provides the foundation for developing and testing contact-based inspection algorithms.
