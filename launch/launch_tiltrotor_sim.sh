#!/bin/bash

# Tiltrotor Drone Gazebo Simulation Launch Script
# This script launches the native Gazebo simulation with your tiltrotor drone

set -e

echo "=== Tiltrotor Drone Simulation Launcher ==="
echo "Setting up environment..."

# Get the current directory (project root)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Source ROS2 if available
if [ -f "/opt/ros/humble/setup.bash" ]; then
    echo "Sourcing ROS2 Humble..."
    source /opt/ros/humble/setup.bash
elif [ -f "/opt/ros/galactic/setup.bash" ]; then
    echo "Sourcing ROS2 Galactic..."
    source /opt/ros/galactic/setup.bash
else
    echo "Warning: ROS2 not found. Continuing with Gazebo only..."
fi

# Set environment variables for Gazebo
export GZ_SIM_RESOURCE_PATH="$PROJECT_ROOT:$PROJECT_ROOT/drone_model:$PROJECT_ROOT/worlds:$GZ_SIM_RESOURCE_PATH"
export GAZEBO_MODEL_PATH="$PROJECT_ROOT:$PROJECT_ROOT/drone_model:$GAZEBO_MODEL_PATH"
export IGN_GAZEBO_RESOURCE_PATH="$PROJECT_ROOT:$PROJECT_ROOT/drone_model:$PROJECT_ROOT/worlds:$IGN_GAZEBO_RESOURCE_PATH"

echo "Project root: $PROJECT_ROOT"
echo "Model paths configured."

# Check if models exist
if [ ! -d "$PROJECT_ROOT/drone_model" ]; then
    echo "ERROR: Tiltrotor drone model not found at $PROJECT_ROOT/drone_model"
    echo "Make sure you're running this script from the project root directory."
    exit 1
fi

if [ ! -f "$PROJECT_ROOT/drone_model/model.sdf" ]; then
    echo "ERROR: Model SDF file not found at $PROJECT_ROOT/drone_model/model.sdf"
    exit 1
fi

echo "Models found. Starting simulation..."

# Launch options
WORLD_FILE="$PROJECT_ROOT/worlds/tiltrotor_world.sdf"
HEADLESS=${HEADLESS:-false}

# Check if world file exists
if [ ! -f "$WORLD_FILE" ]; then
    echo "ERROR: World file not found at $WORLD_FILE"
    exit 1
fi

if [ "$HEADLESS" = "true" ]; then
    echo "Starting headless simulation..."
    ign gazebo -r -s --headless-rendering "$WORLD_FILE"
else
    echo "Starting GUI simulation..."
    echo "Use the following commands in another terminal to control the drone:"
    echo ""
    echo "# Spin all propellers:"
    echo "ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p 'velocity:[500, 500, 500, 500]'"
    echo ""
    echo "# Control tilt joints (examples):"
    echo "ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'"
    echo "ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'"
    echo "ign topic -t /model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'"
    echo "ign topic -t /model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'"
    echo ""

    # Start Gazebo with GUI
    ign gazebo "$WORLD_FILE"
fi
