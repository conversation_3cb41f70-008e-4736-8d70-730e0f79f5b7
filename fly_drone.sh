#!/bin/bash

# Tiltrotor Drone Flight Test Script
# This script demonstrates basic flight maneuvers for the tiltrotor drone

set -e

echo "=== Tiltrotor Drone Flight Test ==="
echo "Make sure the simulation is running first!"
echo ""

# Function to send motor commands
send_motor_command() {
    local speed1=$1
    local speed2=$2
    local speed3=$3
    local speed4=$4
    echo "Setting motor speeds: [$speed1, $speed2, $speed3, $speed4]"
    ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p "velocity:[$speed1, $speed2, $speed3, $speed4]"
}

# Function to set tilt angles (in radians)
set_tilt_angles() {
    local rear_right=$1
    local rear_left=$2
    local front_right=$3
    local front_left=$4
    
    echo "Setting tilt angles: RR=$rear_right, RL=$rear_left, FR=$front_right, FL=$front_left"
    
    ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $rear_right" &
    ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $rear_left" &
    ign topic -t /model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $front_right" &
    ign topic -t /model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $front_left" &
    
    wait
}

# Function to wait with countdown
wait_with_countdown() {
    local seconds=$1
    local message=$2
    echo "$message"
    for ((i=seconds; i>0; i--)); do
        echo -ne "\rWaiting... $i seconds remaining"
        sleep 1
    done
    echo -e "\rDone!                          "
}

echo "Starting flight sequence..."
echo ""

# Phase 1: Initialize - Set all rotors to vertical (helicopter mode)
echo "Phase 1: Initializing - Setting rotors to vertical position"
set_tilt_angles 0 0 0 0
wait_with_countdown 3 "Waiting for tilt servos to reach position..."

# Phase 2: Takeoff
echo ""
echo "Phase 2: Takeoff - Spinning up rotors"
send_motor_command 600 600 600 600
wait_with_countdown 5 "Taking off..."

# Phase 3: Hover
echo ""
echo "Phase 3: Stable hover"
send_motor_command 550 550 550 550
wait_with_countdown 5 "Hovering..."

# Phase 4: Transition to forward flight
echo ""
echo "Phase 4: Transitioning to forward flight mode"
echo "Tilting rotors forward gradually..."

# Gradual tilt forward
set_tilt_angles 0.3 0.3 0.3 0.3
send_motor_command 650 650 650 650
wait_with_countdown 3 "Partial tilt..."

set_tilt_angles 0.8 0.8 0.8 0.8
send_motor_command 700 700 700 700
wait_with_countdown 3 "More tilt..."

set_tilt_angles 1.4 1.4 1.4 1.4
send_motor_command 750 750 750 750
wait_with_countdown 5 "Forward flight mode..."

# Phase 5: Forward flight
echo ""
echo "Phase 5: Forward flight"
send_motor_command 800 800 800 800
wait_with_countdown 8 "Flying forward..."

# Phase 6: Transition back to hover
echo ""
echo "Phase 6: Transitioning back to hover mode"
echo "Tilting rotors back to vertical..."

set_tilt_angles 0.8 0.8 0.8 0.8
send_motor_command 700 700 700 700
wait_with_countdown 3 "Reducing tilt..."

set_tilt_angles 0.3 0.3 0.3 0.3
send_motor_command 600 600 600 600
wait_with_countdown 3 "Almost vertical..."

set_tilt_angles 0 0 0 0
send_motor_command 550 550 550 550
wait_with_countdown 5 "Back to hover mode..."

# Phase 7: Landing
echo ""
echo "Phase 7: Landing"
send_motor_command 400 400 400 400
wait_with_countdown 3 "Descending..."

send_motor_command 200 200 200 200
wait_with_countdown 3 "Landing..."

send_motor_command 0 0 0 0
echo ""
echo "Flight test complete! Drone has landed."
echo ""
echo "You can run this script again or try manual control commands:"
echo ""
echo "Manual control examples:"
echo "# Takeoff:"
echo "ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p 'velocity:[600, 600, 600, 600]'"
echo ""
echo "# Tilt forward:"
echo "ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 1.0'"
echo "ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 1.0'"
echo "ign topic -t /model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 1.0'"
echo "ign topic -t /model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 1.0'"
echo ""
echo "# Land:"
echo "ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p 'velocity:[0, 0, 0, 0]'"
