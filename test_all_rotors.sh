#!/bin/bash

echo "Testing All 4 Tiltrotors"
echo "========================"

# Wait for simulation to start
echo "Waiting for simulation to initialize..."
sleep 3

echo ""
echo "Available topics:"
gz topic -l | grep -E "(motor_speed|tilt|joint)"

echo ""
echo "1. Testing all 4 motors (hover mode)..."
echo "Spinning all rotors at moderate speed..."
gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[600, 600, 600, 600]' &

sleep 3

echo ""
echo "2. Testing individual tilt mechanisms..."
echo "Tilting each rotor individually..."

echo "Tilting left rotor..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_left/cmd_pos -m gz.msgs.Double -p 'data: 0.5' &
sleep 1

echo "Tilting right rotor..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_right/cmd_pos -m gz.msgs.Double -p 'data: 0.5' &
sleep 1

echo "Tilting front left rotor..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_left/cmd_pos -m gz.msgs.Double -p 'data: 0.5' &
sleep 1

echo "Tilting front right rotor..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_right/cmd_pos -m gz.msgs.Double -p 'data: 0.5' &
sleep 2

echo ""
echo "3. Returning all rotors to vertical..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_left/cmd_pos -m gz.msgs.Double -p 'data: 0.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_right/cmd_pos -m gz.msgs.Double -p 'data: 0.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_left/cmd_pos -m gz.msgs.Double -p 'data: 0.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_right/cmd_pos -m gz.msgs.Double -p 'data: 0.0' &

sleep 2

echo ""
echo "4. Testing synchronized tilt (all forward)..."
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_left/cmd_pos -m gz.msgs.Double -p 'data: 1.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_right/cmd_pos -m gz.msgs.Double -p 'data: 1.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_left/cmd_pos -m gz.msgs.Double -p 'data: 1.0' &
gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_right/cmd_pos -m gz.msgs.Double -p 'data: 1.0' &

echo "Increasing thrust for forward flight..."
gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[800, 800, 800, 800]' &

sleep 3

echo ""
echo "5. Stopping motors..."
gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[0, 0, 0, 0]' &

echo ""
echo "Test complete!"
echo ""
echo "Manual control commands:"
echo "------------------------"
echo "# All rotors hover:"
echo "gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[600, 600, 600, 600]'"
echo ""
echo "# Individual tilt controls:"
echo "gz topic -t /model/tiltrotor_drone/joint/tilt_joint_left/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
echo "gz topic -t /model/tiltrotor_drone/joint/tilt_joint_right/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
echo "gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_left/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
echo "gz topic -t /model/tiltrotor_drone/joint/tilt_joint_front_right/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
echo ""
echo "# Stop all:"
echo "gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[0, 0, 0, 0]'"
