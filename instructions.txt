I have a tiltrotor project for contact technology (inspection by contacting sensors attached 
to drone with surfaces and maintain pressure and posture for sometime while tilting the rotors 
(sometime))

I have created a CAD model in FreeCAD then exported meshes in .dae that gazebo supports. 
My aim is to modify PX4 to supoort this geometry, write code for that airframe and necessary
 conigurations. But I want to start with gazebo simulation. 

you can see /Users/<USER>/Desktop/Work/midas/drone_model there I currently have a .dae meshes
 I want to build visual model from those for now. Let me know if I am missing something or 
 if you need something from me. here is working gazebo model for other type of quadrator 
 /Users/<USER>/Desktop/Work/midas/x3_uav a very famous iris model. I want working gazebo model
  for my tilting rotor

mahmood@Mahmoods-MacBook-Air ~ % docker image ls
REPOSITORY     TAG                        IMAGE ID       CREATED         SIZE
althack/ros2   humble-gazebo-2024-05-05   dfa9c0b562bd   12 months ago   5.83GB

we have this image here alread