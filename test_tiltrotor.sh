#!/bin/bash

# Test script for tiltrotor drone simulation
# This script tests the basic functionality of the native tiltrotor simulation

echo "=== Tiltrotor Drone Test Script (Native) ==="

# Check if required tools are available
if ! command -v xmllint &> /dev/null; then
    echo "WARNING: xmllint not found. Installing..."
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y libxml2-utils
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        brew install libxml2
    else
        echo "Please install xmllint manually for XML validation"
    fi
fi

if ! command -v ign &> /dev/null; then
    echo "ERROR: Ignition Gazebo (ign) not found. Please install Gazebo Fortress first."
    exit 1
fi

echo "1. Testing model validation..."
if xmllint --noout drone_model/model.sdf 2>/dev/null; then
    echo "✓ Model SDF is valid"
else
    echo "✗ Model SDF validation failed"
    xmllint --noout drone_model/model.sdf
fi

echo ""
echo "2. Testing world file validation..."
if xmllint --noout worlds/tiltrotor_world.sdf 2>/dev/null; then
    echo "✓ World SDF is valid"
else
    echo "✗ World SDF validation failed"
    xmllint --noout worlds/tiltrotor_world.sdf
fi

echo ""
echo "3. Testing Gazebo model loading (dry run)..."
echo "Starting Ignition Gazebo for 10 seconds to test model loading..."
timeout 10s ign gazebo --verbose -s worlds/tiltrotor_world.sdf &
GAZEBO_PID=$!
sleep 5
if kill -0 $GAZEBO_PID 2>/dev/null; then
    echo "✓ Gazebo started successfully"
    kill $GAZEBO_PID 2>/dev/null || true
    wait $GAZEBO_PID 2>/dev/null || true
else
    echo "✗ Gazebo failed to start"
fi

echo ""
echo "4. Testing file structure..."
echo "Checking required files:"
files=("drone_model/model.sdf" "drone_model/model.config" "worlds/tiltrotor_world.sdf" "launch/launch_tiltrotor_sim.sh")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file exists"
    else
        echo "✗ $file missing"
    fi
done

echo ""
echo "5. Testing mesh files..."
meshes=("drone_model/meshes/body.dae" "drone_model/meshes/propeller.dae" "drone_model/meshes/tilt_rotor.dae")
for mesh in "${meshes[@]}"; do
    if [ -f "$mesh" ]; then
        echo "✓ $mesh exists"
    else
        echo "✗ $mesh missing"
    fi
done

echo ""
echo "=== Manual Testing Instructions ==="
echo "To test the simulation manually:"
echo "1. Start the simulation:"
echo "   ./run_simulation.sh"
echo ""
echo "2. In another terminal, check available topics:"
echo "   ign topic -l"
echo ""
echo "3. Control the drone:"
echo "   # Spin propellers:"
echo "   ign topic -t /command/motor_speed -m ignition.msgs.Actuators -p 'velocity:[500, 500, 500, 500]'"
echo ""
echo "   # Tilt rotors:"
echo "   ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 0.5'"

echo ""
echo "=== Test Complete ==="
