#!/bin/bash

# Test script for tiltrotor drone controls
# This script demonstrates various control commands for the tiltrotor drone

echo "=== Tiltrotor Drone Control Test ==="

# Check if simulation is running
if ! gz topic -l | grep -q "/tiltrotor_drone/command/motor_speed"; then
    echo "ERROR: Simulation not running or motor topics not available"
    echo "Please start the simulation first: ./run_simulation.sh"
    exit 1
fi

echo "Simulation detected. Testing controls..."

echo ""
echo "1. Testing propeller spin (low speed)..."
gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[200, 200]'
sleep 2

echo ""
echo "2. Testing propeller spin (medium speed)..."
gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[400, 400]'
sleep 2

echo ""
echo "3. Testing propeller spin (high speed)..."
gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[600, 600]'
sleep 2

echo ""
echo "4. Testing tilt control - tilting left rotor forward..."
gz topic -t /model/tiltrotor_drone/joint/left_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.5'
sleep 2

echo ""
echo "5. Testing tilt control - tilting right rotor forward..."
gz topic -t /model/tiltrotor_drone/joint/right_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.5'
sleep 2

echo ""
echo "6. Testing tilt control - tilting both rotors back to vertical..."
gz topic -t /model/tiltrotor_drone/joint/left_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.0'
gz topic -t /model/tiltrotor_drone/joint/right_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.0'
sleep 2

echo ""
echo "7. Testing asymmetric propeller control..."
gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[300, 500]'
sleep 2

echo ""
echo "8. Stopping all motors..."
gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[0, 0]'

echo ""
echo "=== Control Test Complete ==="
echo ""
echo "Available control commands:"
echo "# Spin propellers:"
echo "gz topic -t /tiltrotor_drone/command/motor_speed -m gz.msgs.Actuators -p 'velocity:[LEFT_SPEED, RIGHT_SPEED]'"
echo ""
echo "# Tilt left rotor:"
echo "gz topic -t /model/tiltrotor_drone/joint/left_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: ANGLE_RADIANS'"
echo ""
echo "# Tilt right rotor:"
echo "gz topic -t /model/tiltrotor_drone/joint/right_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: ANGLE_RADIANS'"
echo ""
echo "Notes:"
echo "- Motor speeds: 0-1000 (typical range 200-800)"
echo "- Tilt angles: -1.57 to 1.57 radians (-90° to +90°)"
echo "- Positive tilt angles point rotors forward"
echo "- Negative tilt angles point rotors backward"
