#!/bin/bash

# Simple Tiltrotor Drone Flight Demo using Direct Physics Commands
# This script demonstrates flight using Gazebo's built-in physics services

set -e

echo "=== Simple Tiltrotor Flight Demo ==="
echo "This demo uses direct physics commands to make the drone fly"
echo ""

# Function to apply force to the drone
apply_force() {
    local fx=$1
    local fy=$2
    local fz=$3
    local duration=${4:-1000}  # Duration in milliseconds
    
    echo "Applying force: [$fx, $fy, $fz] for ${duration}ms"
    ign service -s /world/tiltrotor_world/set_pose --reqtype ignition.msgs.Pose --reptype ignition.msgs.Boolean --timeout 5000 -r "name: 'tiltrotor_drone', position: {x: 0, y: 0, z: 0.5}"
    
    # Apply force using the physics engine
    timeout ${duration}ms bash -c "
        while true; do
            ign topic -t /world/tiltrotor_world/wrench -m ignition.msgs.EntityWrench -p \"
            entity: {name: 'tiltrotor_drone', type: MODEL},
            wrench: {
                force: {x: $fx, y: $fy, z: $fz},
                torque: {x: 0, y: 0, z: 0}
            }
            \"
            sleep 0.1
        done
    " 2>/dev/null || true
}

# Function to set drone position directly
set_position() {
    local x=$1
    local y=$2
    local z=$3
    local roll=${4:-0}
    local pitch=${5:-0}
    local yaw=${6:-0}
    
    echo "Setting position: [$x, $y, $z] with orientation: [$roll, $pitch, $yaw]"
    ign service -s /world/tiltrotor_world/set_pose --reqtype ignition.msgs.Pose --reptype ignition.msgs.Boolean --timeout 5000 -r "
    name: 'tiltrotor_drone',
    position: {x: $x, y: $y, z: $z},
    orientation: {x: $roll, y: $pitch, z: $yaw, w: 1}
    "
}

# Function to tilt rotors
tilt_rotors() {
    local angle=$1
    echo "Tilting rotors to $angle radians"
    
    ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $angle" &
    ign topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $angle" &
    ign topic -t /model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $angle" &
    ign topic -t /model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos -m ignition.msgs.Double -p "data: $angle" &
    
    wait
}

# Function to wait with countdown
wait_with_countdown() {
    local seconds=$1
    local message=$2
    echo "$message"
    for ((i=seconds; i>0; i--)); do
        echo -ne "\rWaiting... $i seconds remaining"
        sleep 1
    done
    echo -e "\rDone!                          "
}

echo "Starting flight demonstration..."
echo ""

# Phase 1: Reset position
echo "Phase 1: Resetting drone position"
set_position 0 0 0.5 0 0 0
tilt_rotors 0
wait_with_countdown 3 "Initializing..."

# Phase 2: Takeoff simulation
echo ""
echo "Phase 2: Takeoff (simulated with upward force)"
apply_force 0 0 50 2000
wait_with_countdown 2 "Taking off..."

# Phase 3: Hover at higher altitude
echo ""
echo "Phase 3: Moving to hover position"
set_position 0 0 3 0 0 0
wait_with_countdown 3 "Hovering..."

# Phase 4: Forward flight preparation
echo ""
echo "Phase 4: Transitioning to forward flight"
echo "Tilting rotors forward..."
tilt_rotors 1.2
wait_with_countdown 2 "Tilting rotors..."

# Phase 5: Forward flight
echo ""
echo "Phase 5: Forward flight simulation"
set_position 5 0 3 0 0.2 0
wait_with_countdown 4 "Flying forward..."

# Phase 6: Lateral movement
echo ""
echo "Phase 6: Lateral movement"
set_position 5 3 3 0.1 0.2 0.3
wait_with_countdown 3 "Moving laterally..."

# Phase 7: Return and hover
echo ""
echo "Phase 7: Returning to hover"
tilt_rotors 0
set_position 0 0 3 0 0 0
wait_with_countdown 3 "Returning to hover..."

# Phase 8: Landing
echo ""
echo "Phase 8: Landing"
set_position 0 0 0.5 0 0 0
wait_with_countdown 2 "Descending..."

set_position 0 0 0.1 0 0 0
wait_with_countdown 2 "Landing..."

echo ""
echo "Flight demonstration complete!"
echo ""
echo "Manual control examples:"
echo ""
echo "# Move drone to specific position:"
echo "ign service -s /world/tiltrotor_world/set_pose --reqtype ignition.msgs.Pose --reptype ignition.msgs.Boolean --timeout 5000 -r \"name: 'tiltrotor_drone', position: {x: 2, y: 1, z: 3}\""
echo ""
echo "# Tilt rotors:"
echo "ign topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m ignition.msgs.Double -p 'data: 1.0'"
echo ""
echo "# Apply continuous force (run in background):"
echo "while true; do ign topic -t /world/tiltrotor_world/wrench -m ignition.msgs.EntityWrench -p \"entity: {name: 'tiltrotor_drone', type: MODEL}, wrench: {force: {x: 0, y: 0, z: 20}}\" ; sleep 0.1; done"
